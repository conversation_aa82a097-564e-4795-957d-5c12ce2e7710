// middleware/syncGoogleUser.js
import User from '../models/User.js';

/**
 * Middleware để tự động tạo/cập nhật user record trong MongoDB cho Google users
 * Chạy sau khi Firebase auth đã verify token thành công
 */
export const syncGoogleUser = async (req, res, next) => {
  try {
    // Chỉ xử lý nếu là Google user (có Firebase UID)
    if (!req.user || !req.user.uid) {
      return next();
    }

    const { uid, email, name } = req.user;

    console.log(`🔄 Syncing Google user: ${uid} (${email})`);

    // Tìm hoặc tạo user record trong MongoDB
    let user = await User.findById(uid);

    if (!user) {
      // Tạo user mới với Firebase UID làm _id
      user = new User({
        _id: uid, // Sử dụng Firebase UID làm _id
        email: email,
        name: name || email.split('@')[0], // Fallback name từ email
        userType: 'google',
        firebaseUID: uid,
        cartData: [], // Khởi tạo mảng cart rỗng
        address: [] // Khởi tạo mảng address rỗng
      });

      await user.save();
      console.log(`✅ Created new Google user record: ${uid}`);
    } else {
      // Cập nhật thông tin nếu cần
      let needUpdate = false;
      
      if (user.email !== email) {
        user.email = email;
        needUpdate = true;
      }
      
      if (name && user.name !== name) {
        user.name = name;
        needUpdate = true;
      }

      if (needUpdate) {
        await user.save();
        console.log(`✅ Updated Google user record: ${uid}`);
      }
    }

    // Đảm bảo req.body có userId
    if (!req.body) {
      req.body = {};
    }
    req.body.userId = uid;

    next();
  } catch (error) {
    console.error('❌ Error syncing Google user:', error);
    
    // Không block request, chỉ log error
    next();
  }
};
