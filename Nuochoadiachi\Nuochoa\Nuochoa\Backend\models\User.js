import mongoose from 'mongoose';

const userSchema = new mongoose.Schema({
  _id: {
    type: String, // Cho phép sử dụng Firebase UID làm _id
    required: true
  },
  username: {
    type: String,
    required: function() {
      return this.userType === 'regular'; // Chỉ required cho regular users
    }
  },
  password: {
    type: String,
    required: function() {
      return this.userType === 'regular'; // Chỉ required cho regular users
    }
  },
  email: { type: String, required: true },
  name: { type: String, required: true }, // Thêm name field cho Google users
  userType: {
    type: String,
    enum: ['regular', 'google'],
    default: 'regular'
  },
  firebaseUID: { type: String }, // Lưu Firebase UID cho Google users
  address: [
    {
      firstName: String,
      lastName: String,
      email: String,
      street: String,
      city: String,
      state: String,
      zipcode: String,
      country: String,
      phone: String,
    }
  ]
}, {
  timestamps: true,
  _id: false // Tắt auto-generate _id để có thể sử dụng custom _id
});

// Index để tối ưu tìm kiếm
userSchema.index({ email: 1 });
userSchema.index({ firebaseUID: 1 });

export default mongoose.model('User', userSchema);
