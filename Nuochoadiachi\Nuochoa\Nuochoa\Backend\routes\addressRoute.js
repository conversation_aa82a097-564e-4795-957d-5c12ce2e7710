// routes/addressRoutes.js

import express from 'express';
import { addAddress, getAddressList, deleteAddress } from '../controllers/addressController.js';
import auth from '../middleware/auth.js'; // Import unified auth middleware

const router = express.Router();

// Route thêm địa chỉ - cần authentication để sync Google users
router.post('/add', auth, addAddress);

// Route lấy danh sách địa chỉ
router.get('/list/:userId', getAddressList);

// Route xóa địa chỉ
router.delete('/delete/:userId/:addressId', deleteAddress);

export default router;
