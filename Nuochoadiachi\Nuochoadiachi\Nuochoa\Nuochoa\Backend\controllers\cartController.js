import User from "../models/User.js";
import userModel from "../models/UserModel.js";
import productModel from "../models/productModels.js";

// Helper function để tìm user trong cả hai collections
const findUserById = async (userId) => {
  try {
    // Thử tìm trong User collection trước (cho Google users và regular users mới)
    let user = await User.findById(userId);
    if (user) {
      console.log(`✅ Found user in User collection: ${userId} (${user.userType || 'unknown'})`);
      return { user, isNewModel: true };
    }

    // N<PERSON>u không tìm thấy, thử tìm trong UserModel collection (cho regular users cũ)
    user = await userModel.findById(userId);
    if (user) {
      console.log(`✅ Found user in UserModel collection: ${userId}`);
      return { user, isNewModel: false };
    }

    console.log(`❌ User not found in any collection: ${userId}`);
    return { user: null, isNewModel: false };
  } catch (error) {
    console.error("❌ Error finding user:", error);
    return { user: null, isNewModel: false };
  }
};

// Helper function để update cart data
const updateUserCart = async (userId, cartData, isNewModel) => {
  if (isNewModel) {
    return await User.findByIdAndUpdate(userId, { cartData });
  } else {
    return await userModel.findByIdAndUpdate(userId, { cartData });
  }
};

// Thêm sản phẩm vào giỏ hàng
const addToCart = async (req, res) => {
  try {
    const { userId, itemId, size, quantity, productData } = req.body;

    // Kiểm tra tồn kho trước khi thêm vào giỏ hàng
    const product = await productModel.findById(itemId);
    if (!product) {
      return res.json({ success: false, message: "Sản phẩm không tồn tại" });
    }

    const availableStock = product.quantity || 0;
    if (availableStock <= 0) {
      return res.json({ success: false, message: "Sản phẩm đã hết hàng" });
    }

    const { user: userData, isNewModel } = await findUserById(userId);
    if (!userData) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    let cartData = userData.cartData || [];

    // Tìm sản phẩm đã có trong giỏ hàng
    const existingItemIndex = cartData.findIndex(
      item => item._id === itemId && item.size === size
    );

    const newQuantity = Number(quantity) || 0;
    let totalQuantityAfterAdd = newQuantity;
    if (existingItemIndex !== -1) {
      const currentQty = Number(cartData[existingItemIndex].quantity) || 0;
      totalQuantityAfterAdd = currentQty + newQuantity;
    }

    // Kiểm tra không vượt quá tồn kho
    if (totalQuantityAfterAdd > availableStock) {
      const currentQty = existingItemIndex !== -1 ? Number(cartData[existingItemIndex].quantity) || 0 : 0;
      const maxCanAdd = availableStock - currentQty;
      return res.json({
        success: false,
        message: `Chỉ có thể thêm tối đa ${maxCanAdd} sản phẩm nữa (tồn kho: ${availableStock})`
      });
    }

    if (existingItemIndex !== -1) {
      // Nếu đã có, cộng dồn số lượng
      const currentQty = Number(cartData[existingItemIndex].quantity) || 0;
      cartData[existingItemIndex].quantity = currentQty + newQuantity;
    } else {
      // Nếu chưa có, thêm mới
      cartData.push({
        _id: itemId,
        name: productData.name,
        image: productData.image,
        originalPrice: productData.originalPrice || productData.price,
        price: productData.price,
        hasPromotion: productData.hasPromotion || false,
        promotion: productData.promotion || null,
        discountPercentage: productData.promotion?.discountPercentage || 0,
        size: size,
        quantity: newQuantity,
        availableStock: availableStock // Lưu thông tin tồn kho
      });
    }

    // Cập nhật cart data trong model đúng
    await updateUserCart(userId, cartData, isNewModel);

    res.json({ success: true, message: "Đã thêm vào giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi thêm vào giỏ hàng" });
  }
};

// Xóa sản phẩm khỏi giỏ hàng
const removeFromCart = async (req, res) => {
  try {
    const { userId, itemId, size } = req.body;

    const { user: userData, isNewModel } = await findUserById(userId);
    if (!userData) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    let cartData = userData.cartData || [];

    // Lọc bỏ sản phẩm cần xóa
    cartData = cartData.filter(item => !(item._id === itemId && item.size === size));

    await updateUserCart(userId, cartData, isNewModel);
    res.json({ success: true, message: "Đã xóa khỏi giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi xóa khỏi giỏ hàng" });
  }
};

// Cập nhật số lượng sản phẩm trong giỏ hàng
const updateCartQuantity = async (req, res) => {
  try {
    const { userId, itemId, size, quantity } = req.body;
    const newQuantity = Number(quantity) || 0;

    // Kiểm tra tồn kho nếu quantity > 0
    if (newQuantity > 0) {
      const product = await productModel.findById(itemId);
      if (!product) {
        return res.json({ success: false, message: "Sản phẩm không tồn tại" });
      }

      const availableStock = product.quantity || 0;
      if (newQuantity > availableStock) {
        return res.json({
          success: false,
          message: `Tồn kho chỉ còn ${availableStock} sản phẩm`
        });
      }
    }

    const { user: userData, isNewModel } = await findUserById(userId);
    if (!userData) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    let cartData = userData.cartData || [];

    // Tìm và cập nhật số lượng
    const itemIndex = cartData.findIndex(item => item._id === itemId && item.size === size);

    if (itemIndex !== -1) {
      if (newQuantity <= 0) {
        // Nếu số lượng <= 0, xóa sản phẩm
        cartData.splice(itemIndex, 1);
      } else {
        // Cập nhật số lượng và thông tin tồn kho
        cartData[itemIndex].quantity = newQuantity;
        if (product) {
          cartData[itemIndex].availableStock = product.quantity;
        }
      }
    }

    await updateUserCart(userId, cartData, isNewModel);
    res.json({ success: true, message: "Đã cập nhật giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi cập nhật giỏ hàng" });
  }
};

// Lấy giỏ hàng của user
const getCart = async (req, res) => {
  try {
    const userId = req.body.userId || req.user?.userId || req.user?.uid;

    console.log(`🛒 Getting cart for user: ${userId}`);

    if (!userId) {
      return res.json({ success: false, message: "Không tìm thấy thông tin user" });
    }

    const { user: userData, isNewModel } = await findUserById(userId);
    if (!userData) {
      console.log(`❌ User not found: ${userId}`);
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    let cartData = userData.cartData || [];
    console.log(`✅ Found cart with ${cartData.length} items for user: ${userId}`);

    res.json({ success: true, cartData });

  } catch (error) {
    console.error("❌ Error getting cart:", error);
    res.json({ success: false, message: "Lỗi khi lấy giỏ hàng" });
  }
};

// Đồng bộ giỏ hàng từ client lên server
const syncCart = async (req, res) => {
  try {
    const { userId, cartData } = req.body;

    const { user: userData, isNewModel } = await findUserById(userId);
    if (!userData) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    // Cập nhật toàn bộ giỏ hàng
    await updateUserCart(userId, cartData || [], isNewModel);
    res.json({ success: true, message: "Đã đồng bộ giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi đồng bộ giỏ hàng" });
  }
};

// Xóa toàn bộ giỏ hàng
const clearCart = async (req, res) => {
  try {
    const { userId } = req.body;

    const { user: userData, isNewModel } = await findUserById(userId);
    if (!userData) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    await updateUserCart(userId, [], isNewModel);
    res.json({ success: true, message: "Đã xóa toàn bộ giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi xóa giỏ hàng" });
  }
};

export { 
  addToCart, 
  removeFromCart, 
  updateCartQuantity, 
  getCart, 
  syncCart, 
  clearCart 
};
