// test-imports.js - Test các imports để đảm bảo không có lỗi
console.log("🧪 Testing imports...");

try {
  console.log("1. Testing User model...");
  const User = await import('./models/User.js');
  console.log("✅ User model imported successfully");

  console.log("2. Testing syncGoogleUser middleware...");
  const { syncGoogleUser } = await import('./middleware/syncGoogleUser.js');
  console.log("✅ syncGoogleUser middleware imported successfully");

  console.log("3. Testing authFirebase middleware...");
  const authFirebase = await import('./middleware/authFirebase.js');
  console.log("✅ authFirebase middleware imported successfully");

  console.log("4. Testing address controller...");
  const addressController = await import('./controllers/addressController.js');
  console.log("✅ Address controller imported successfully");

  console.log("5. Testing address routes...");
  const addressRoutes = await import('./routes/addressRoute.js');
  console.log("✅ Address routes imported successfully");

  console.log("🎉 All imports successful!");
} catch (error) {
  console.error("❌ Import error:", error);
  process.exit(1);
}
