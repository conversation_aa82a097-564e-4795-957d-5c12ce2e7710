// test-google-user.js - Test tạo Google user
import mongoose from 'mongoose';
import User from './models/User.js';

const testGoogleUser = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/food-del', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log("✅ Connected to MongoDB");

    const testUserId = "2lVnmJKqrRZ9hVrSSrdejDi2g1z2";
    
    // Kiểm tra xem user đã tồn tại chưa
    let user = await User.findById(testUserId);
    
    if (user) {
      console.log("✅ User already exists:", user);
    } else {
      console.log("❌ User not found, creating new user...");
      
      // Tạo user mới
      user = new User({
        _id: testUserId,
        email: "<EMAIL>",
        name: "Test User",
        userType: 'google',
        firebaseUID: testUserId,
        cartData: [],
        address: []
      });

      await user.save();
      console.log("✅ Created new Google user:", user);
    }

    // Test cart operations
    console.log("🛒 Testing cart operations...");
    
    // Add item to cart
    user.cartData.push({
      _id: "test-product-id",
      name: "Test Product",
      image: "test.jpg",
      price: 100,
      size: "M",
      quantity: 1
    });

    await user.save();
    console.log("✅ Added item to cart");

    // Fetch user again to verify
    const updatedUser = await User.findById(testUserId);
    console.log("✅ Cart data:", updatedUser.cartData);

    await mongoose.disconnect();
    console.log("✅ Test completed successfully");

  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
};

testGoogleUser();
